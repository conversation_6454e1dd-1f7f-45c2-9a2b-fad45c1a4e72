import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { MrpProduction } from '../models/mrp.production.model';
import { ScanPackageModal } from '../components/modals/scan-package/scan-package.modal';
import { StockQuantPackage } from '../models/stock.quant.package';

@Component({
  selector: 'app-pelatura-production',
  standalone: false,
  templateUrl: './pelatura-production.component.html',
  styleUrl: './pelatura-production.component.scss'
})
export class PelaturaProductionComponent implements OnInit {
  mrpProduction: any;
  @ViewChild('modal') modal: ScanPackageModal;
  
  constructor(private activatedRoute: ActivatedRoute,private odooEM: OdooEntityManager) { }

  ngOnInit(): void {
  
      this.activatedRoute.url.subscribe(async url => {
      await this.load()
    });
  }

  async load() {
    var p = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [
      ['id', '=', this.activatedRoute.snapshot.params['id']]
    ])))[0]
    
    this.mrpProduction = p
  }

  openScanModal() {
    this.modal.show()
  }

 async onBarcode(code: string) {
    console.log("Scanned Package Code: ", code);
    // this.packageCode = code;
    let quant_package = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [['name', '=', code]]));
    if (!quant_package.length) {
      this.error = true;
      this.toast.error("Non trovato o codifica errata!");
      return;
    }

    await firstValueFrom(this.odooEM.resolve(quant_package[0]?.quant_ids));

    console.log(quant_package);

    if (quant_package[0]?.quant_ids.values && quant_package[0]?.quant_ids.values.length) {
      for (var sq of quant_package[0]?.quant_ids?.values) {
        await this.handleSingleLot(sq);

        this.addToByProducts(sq.product_id.name, this.getSaccoBlu() || undefined);
        break;
      }
    } else {
      this.toast.error("Nessuna quantità nel pacco!");
    }

    await this.load();
  }

}
